<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Booking Confirmation - Togeda.ai</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #F5F5F5;
            color: #212121;
            line-height: 1.6;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            background: linear-gradient(135deg, #00796B, #004D40);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        .header .subtitle {
            margin: 10px 0 0 0;
            font-size: 16px;
            opacity: 0.9;
        }
        .content {
            padding: 30px 20px;
        }
        .booking-card {
            background-color: #F5F5F5;
            border-left: 4px solid #FFD54F;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        .booking-detail {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 8px 0;
            border-bottom: 1px solid #e0e0e0;
        }
        .booking-detail:last-child {
            border-bottom: none;
        }
        .booking-detail .label {
            font-weight: 600;
            color: #00796B;
            flex: 1;
        }
        .booking-detail .value {
            flex: 2;
            text-align: right;
        }
        .highlight {
            background-color: #FFD54F;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 600;
        }
        .host-info {
            background-color: #E8F5E8;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .host-info h3 {
            margin: 0 0 15px 0;
            color: #00796B;
            font-size: 18px;
        }
        .contact-item {
            margin: 8px 0;
            display: flex;
            align-items: center;
        }
        .contact-item .icon {
            width: 20px;
            margin-right: 10px;
            color: #00796B;
        }
        .button {
            display: inline-block;
            background: linear-gradient(135deg, #00796B, #004D40);
            color: white;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            text-align: center;
            margin: 20px 0;
            transition: transform 0.2s;
        }
        .button:hover {
            transform: translateY(-2px);
        }
        .footer {
            background-color: #212121;
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .footer p {
            margin: 5px 0;
        }
        .footer .brand {
            font-size: 20px;
            font-weight: 600;
            color: #FFD54F;
        }
        .cancellation-policy {
            background-color: #FFF3E0;
            border: 1px solid #FFD54F;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .cancellation-policy h4 {
            margin: 0 0 10px 0;
            color: #F57C00;
        }
        @media (max-width: 600px) {
            .container {
                margin: 0;
                box-shadow: none;
            }
            .header, .content, .footer {
                padding: 20px 15px;
            }
            .booking-detail {
                flex-direction: column;
            }
            .booking-detail .value {
                text-align: left;
                margin-top: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🎉 Booking Confirmed!</h1>
            <p class="subtitle">Your local experience is all set</p>
        </div>

        <!-- Content -->
        <div class="content">
            <p>Hi <strong>{{guestName}}</strong>,</p>
            
            <p>Great news! Your booking for <strong>{{experienceTitle}}</strong> has been confirmed. We're excited for you to enjoy this amazing local experience!</p>

            <!-- Booking Details Card -->
            <div class="booking-card">
                <h3 style="margin: 0 0 15px 0; color: #00796B;">📋 Booking Details</h3>
                
                <div class="booking-detail">
                    <span class="label">Booking ID:</span>
                    <span class="value highlight">{{bookingId}}</span>
                </div>
                
                <div class="booking-detail">
                    <span class="label">Experience:</span>
                    <span class="value">{{experienceTitle}}</span>
                </div>
                
                <div class="booking-detail">
                    <span class="label">Date:</span>
                    <span class="value">{{bookingDate}}</span>
                </div>
                
                <div class="booking-detail">
                    <span class="label">Time:</span>
                    <span class="value">{{bookingTime}}</span>
                </div>
                
                <div class="booking-detail">
                    <span class="label">Guests:</span>
                    <span class="value">{{guestCount}} {{guestCount == 1 ? 'person' : 'people'}}</span>
                </div>
                
                <div class="booking-detail">
                    <span class="label">Location:</span>
                    <span class="value">{{experienceLocation}}</span>
                </div>
                
                <div class="booking-detail">
                    <span class="label">Total Amount:</span>
                    <span class="value highlight">${{totalAmount}}</span>
                </div>
            </div>

            <!-- Host Information -->
            <div class="host-info">
                <h3>👤 Your Host: {{hostName}}</h3>
                <p>{{experienceDescription}}</p>
                
                {{#if hostEmail}}
                <div class="contact-item">
                    <span class="icon">📧</span>
                    <span>{{hostEmail}}</span>
                </div>
                {{/if}}
                
                {{#if hostPhone}}
                <div class="contact-item">
                    <span class="icon">📱</span>
                    <span>{{hostPhone}}</span>
                </div>
                {{/if}}
            </div>

            {{#if specialRequests}}
            <div style="background-color: #E3F2FD; padding: 15px; border-radius: 8px; margin: 20px 0;">
                <h4 style="margin: 0 0 10px 0; color: #1976D2;">📝 Special Requests</h4>
                <p style="margin: 0;">{{specialRequests}}</p>
            </div>
            {{/if}}

            <!-- Cancellation Policy -->
            <div class="cancellation-policy">
                <h4>📋 Cancellation Policy</h4>
                <p>{{cancellationPolicy}}</p>
                <p><strong>Need to cancel or modify your booking?</strong> Please contact us at <a href="mailto:<EMAIL>" style="color: #F57C00;"><EMAIL></a></p>
            </div>

            <!-- Call to Action -->
            <div style="text-align: center; margin: 30px 0;">
                <a href="{{bookingDetailsUrl}}" class="button">View Booking Details</a>
            </div>

            <p>We hope you have an amazing time! If you have any questions before your experience, don't hesitate to reach out to your host or contact our support team.</p>
            
            <p>Safe travels,<br>
            <strong>The Togeda.ai Team</strong></p>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p class="brand">Togeda.ai</p>
            <p>Connecting travelers with authentic local experiences</p>
            <p style="font-size: 12px; opacity: 0.8;">
                This email was sent regarding your booking {{bookingId}}.<br>
                If you have any questions, contact <NAME_EMAIL>
            </p>
        </div>
    </div>
</body>
</html>
