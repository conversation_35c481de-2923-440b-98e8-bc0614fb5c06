<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Booking Received - Togeda.ai</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #F5F5F5;
            color: #212121;
            line-height: 1.6;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            background: linear-gradient(135deg, #00796B, #004D40);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        .header .subtitle {
            margin: 10px 0 0 0;
            font-size: 16px;
            opacity: 0.9;
        }
        .content {
            padding: 30px 20px;
        }
        .booking-card {
            background-color: #F5F5F5;
            border-left: 4px solid #FFD54F;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        .booking-detail {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 8px 0;
            border-bottom: 1px solid #e0e0e0;
        }
        .booking-detail:last-child {
            border-bottom: none;
        }
        .booking-detail .label {
            font-weight: 600;
            color: #00796B;
            flex: 1;
        }
        .booking-detail .value {
            flex: 2;
            text-align: right;
        }
        .highlight {
            background-color: #FFD54F;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 600;
        }
        .guest-info {
            background-color: #E8F5E8;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .guest-info h3 {
            margin: 0 0 15px 0;
            color: #00796B;
            font-size: 18px;
        }
        .contact-item {
            margin: 8px 0;
            display: flex;
            align-items: center;
        }
        .contact-item .icon {
            width: 20px;
            margin-right: 10px;
            color: #00796B;
        }
        .button {
            display: inline-block;
            background: linear-gradient(135deg, #00796B, #004D40);
            color: white;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            text-align: center;
            margin: 20px 0;
            transition: transform 0.2s;
        }
        .button:hover {
            transform: translateY(-2px);
        }
        .button.secondary {
            background: linear-gradient(135deg, #FFD54F, #FFC107);
            color: #212121;
        }
        .footer {
            background-color: #212121;
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .footer p {
            margin: 5px 0;
        }
        .footer .brand {
            font-size: 20px;
            font-weight: 600;
            color: #FFD54F;
        }
        .alert-box {
            background-color: #E8F5E8;
            border: 2px solid #4CAF50;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }
        .alert-box .amount {
            font-size: 24px;
            font-weight: 700;
            color: #2E7D32;
            margin: 10px 0;
        }
        .action-buttons {
            text-align: center;
            margin: 30px 0;
        }
        .action-buttons .button {
            margin: 10px;
            display: inline-block;
        }
        @media (max-width: 600px) {
            .container {
                margin: 0;
                box-shadow: none;
            }
            .header, .content, .footer {
                padding: 20px 15px;
            }
            .booking-detail {
                flex-direction: column;
            }
            .booking-detail .value {
                text-align: left;
                margin-top: 5px;
            }
            .action-buttons .button {
                display: block;
                margin: 10px 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🎉 New Booking Received!</h1>
            <p class="subtitle">You have a new guest for your experience</p>
        </div>

        <!-- Content -->
        <div class="content">
            <p>Hi <strong>{{hostName}}</strong>,</p>
            
            <p>Exciting news! You've received a new booking for your experience <strong>{{experienceTitle}}</strong>. Here are the details:</p>

            <!-- Earnings Alert -->
            <div class="alert-box">
                <p style="margin: 0; font-size: 16px;">💰 <strong>You've earned:</strong></p>
                <div class="amount">${{totalAmount}}</div>
                <p style="margin: 0; font-size: 14px; opacity: 0.8;">Payment will be processed and transferred to your account</p>
            </div>

            <!-- Booking Details Card -->
            <div class="booking-card">
                <h3 style="margin: 0 0 15px 0; color: #00796B;">📋 Booking Details</h3>
                
                <div class="booking-detail">
                    <span class="label">Booking ID:</span>
                    <span class="value highlight">{{bookingId}}</span>
                </div>
                
                <div class="booking-detail">
                    <span class="label">Experience:</span>
                    <span class="value">{{experienceTitle}}</span>
                </div>
                
                <div class="booking-detail">
                    <span class="label">Date:</span>
                    <span class="value">{{bookingDate}}</span>
                </div>
                
                <div class="booking-detail">
                    <span class="label">Time:</span>
                    <span class="value">{{bookingTime}}</span>
                </div>
                
                <div class="booking-detail">
                    <span class="label">Number of Guests:</span>
                    <span class="value">{{guestCount}} {{guestCount == 1 ? 'person' : 'people'}}</span>
                </div>
                
                <div class="booking-detail">
                    <span class="label">Location:</span>
                    <span class="value">{{experienceLocation}}</span>
                </div>
                
                <div class="booking-detail">
                    <span class="label">Total Earnings:</span>
                    <span class="value highlight">${{totalAmount}}</span>
                </div>
            </div>

            <!-- Guest Information -->
            <div class="guest-info">
                <h3>👤 Guest Information: {{guestName}}</h3>
                
                <div class="contact-item">
                    <span class="icon">📧</span>
                    <span>{{guestEmail}}</span>
                </div>
                
                {{#if guestPhone}}
                <div class="contact-item">
                    <span class="icon">📱</span>
                    <span>{{guestPhone}}</span>
                </div>
                {{/if}}
            </div>

            {{#if specialRequests}}
            <div style="background-color: #E3F2FD; padding: 15px; border-radius: 8px; margin: 20px 0;">
                <h4 style="margin: 0 0 10px 0; color: #1976D2;">📝 Special Requests from Guest</h4>
                <p style="margin: 0; font-style: italic;">"{{specialRequests}}"</p>
            </div>
            {{/if}}

            <!-- Action Buttons -->
            <div class="action-buttons">
                <a href="{{hostDashboardUrl}}" class="button">View in Dashboard</a>
                <a href="mailto:{{guestEmail}}?subject=Re: Your booking for {{experienceTitle}} ({{bookingId}})" class="button secondary">Contact Guest</a>
            </div>

            <!-- Next Steps -->
            <div style="background-color: #FFF3E0; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h4 style="margin: 0 0 15px 0; color: #F57C00;">📋 Next Steps</h4>
                <ul style="margin: 0; padding-left: 20px;">
                    <li>Review the booking details and special requests</li>
                    <li>Prepare for your experience on {{bookingDate}} at {{bookingTime}}</li>
                    <li>Contact the guest if you need to clarify anything</li>
                    <li>Ensure you have everything ready for {{guestCount}} {{guestCount == 1 ? 'guest' : 'guests'}}</li>
                </ul>
            </div>

            <p>We're excited to see another successful booking for your experience! Remember, providing an exceptional experience leads to great reviews and more bookings.</p>
            
            <p>If you have any questions or need support, don't hesitate to reach out to our host support team.</p>
            
            <p>Best regards,<br>
            <strong>The Togeda.ai Host Team</strong></p>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p class="brand">Togeda.ai</p>
            <p>Empowering hosts to share amazing local experiences</p>
            <p style="font-size: 12px; opacity: 0.8;">
                This email was sent regarding booking {{bookingId}} for your experience.<br>
                Host support: <a href="mailto:<EMAIL>" style="color: #FFD54F;"><EMAIL></a>
            </p>
        </div>
    </div>
</body>
</html>
