<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>New Booking Received - Togeda.ai</title>
    <style>
      /* Email-safe CSS with full responsive support */
      table {
        border-collapse: collapse;
        mso-table-lspace: 0pt;
        mso-table-rspace: 0pt;
      }

      img {
        border: 0;
        height: auto;
        line-height: 100%;
        outline: none;
        text-decoration: none;
        max-width: 100%;
      }

      body {
        margin: 0;
        padding: 0;
        width: 100% !important;
        -webkit-text-size-adjust: 100%;
        -ms-text-size-adjust: 100%;
        background-color: #f5f5f5;
      }

      .email-container {
        max-width: 600px;
        margin: 0 auto;
        background-color: #ffffff;
        font-family: Arial, sans-serif;
        width: 100% !important;
        min-width: 320px;
      }

      .email-header {
        background-color: #00796b;
        padding-top: 10px;
        text-align: center;
      }

      .email-header h1 {
        color: #ffffff;
        margin: 0;
        font-size: 24px;
        font-weight: 600;
      }

      .email-body {
        padding: 30px 20px;
        background-color: #ffffff;
      }

      .email-content {
        line-height: 1.6;
        color: #333333;
      }

      .email-content h2 {
        color: #00796b;
        margin-bottom: 15px;
        font-size: 20px;
      }

      .email-content p {
        margin-bottom: 15px;
        font-size: 16px;
      }

      .email-content ul {
        padding-left: 20px;
        margin-bottom: 15px;
      }

      .email-content li {
        margin-bottom: 8px;
        font-size: 16px;
      }

      .cta-button {
        display: inline-block;
        background-color: #ffd54f;
        background-image: linear-gradient(to bottom, #ffdb6a, #ffc107);
        color: #333333 !important;
        padding: 12px 30px;
        text-decoration: none;
        border-radius: 25px;
        font-weight: 600;
        margin: 20px 0;
        font-size: 16px;
        min-width: 120px;
        text-align: center;
        box-sizing: border-box;
        border: 1px solid #ffc107;
      }

      .cta-button:hover {
        background-color: #ffc107;
        background-image: linear-gradient(to bottom, #ffc107, #ffa000);
      }

      .cta-button.secondary {
        background-color: #00796b;
        background-image: linear-gradient(to bottom, #00796b, #004d40);
        color: #ffffff !important;
        border: 1px solid #00796b;
        margin: 10px;
      }

      .details-box {
        background-color: #f8f9fa;
        border-left: 4px solid #00796b;
        padding: 15px;
        margin: 20px 0;
        border-radius: 0 8px 8px 0;
      }

      .details-box h3 {
        color: #00796b;
        margin: 0 0 10px 0;
        font-size: 18px;
      }

      .details-box p {
        margin: 5px 0;
        font-size: 14px;
      }

      .booking-detail {
        display: flex;
        justify-content: space-between;
        margin: 8px 0;
        padding: 6px 0;
        border-bottom: 1px solid #e0e0e0;
      }

      .booking-detail:last-child {
        border-bottom: none;
      }

      .booking-detail .label {
        font-weight: 600;
        color: #00796b;
        flex: 1;
      }

      .booking-detail .value {
        flex: 1;
        text-align: right;
        font-weight: 500;
      }

      .highlight {
        background-color: #ffd54f;
        padding: 2px 6px;
        border-radius: 4px;
        font-weight: 600;
      }

      .guest-info {
        background-color: #e8f5e8;
        padding: 15px;
        border-radius: 8px;
        margin: 20px 0;
      }

      .contact-item {
        margin: 6px 0;
        font-size: 14px;
      }

      .contact-item .icon {
        display: inline-block;
        width: 16px;
        margin-right: 8px;
        color: #00796b;
      }

      .earnings-alert {
        background-color: #e8f5e8;
        border: 2px solid #4caf50;
        padding: 15px;
        border-radius: 8px;
        margin: 20px 0;
        text-align: center;
      }

      .earnings-alert .amount {
        font-size: 24px;
        font-weight: 700;
        color: #2e7d32;
        margin: 10px 0;
      }

      .action-buttons {
        text-align: center;
        margin: 30px 0;
      }

      .email-footer {
        background-color: #00796b;
        color: #ffffff;
        padding: 20px;
        text-align: center;
        font-size: 14px;
      }

      .email-footer a {
        color: #b2dfdb;
        text-decoration: none;
      }

      /* Mobile-first responsive design */
      @media only screen and (max-width: 640px) {
        .email-container {
          width: 100% !important;
          max-width: 100% !important;
        }

        .email-header {
          padding: 15px 10px !important;
        }

        .email-header h1 {
          font-size: 20px !important;
        }

        .email-body {
          padding: 20px 15px !important;
        }

        .email-content h2 {
          font-size: 18px !important;
          line-height: 1.3 !important;
        }

        .email-content p {
          font-size: 14px !important;
          line-height: 1.5 !important;
        }

        .cta-button {
          display: block !important;
          width: 100% !important;
          max-width: 280px !important;
          margin: 20px auto !important;
          padding: 15px 20px !important;
          font-size: 16px !important;
          text-align: center !important;
          box-sizing: border-box !important;
        }

        .details-box {
          padding: 12px !important;
          margin: 15px 0 !important;
        }

        .details-box h3 {
          font-size: 16px !important;
        }

        .details-box p {
          font-size: 13px !important;
        }

        .booking-detail {
          flex-direction: column !important;
        }

        .booking-detail .value {
          text-align: left !important;
          margin-top: 3px !important;
        }

        .guest-info {
          padding: 12px !important;
        }

        .action-buttons .cta-button {
          display: block !important;
          margin: 10px auto !important;
        }

        .email-footer {
          padding: 15px 10px !important;
          font-size: 13px !important;
        }
      }

      /* Large screens optimization */
      @media only screen and (min-width: 641px) {
        .email-container {
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
          border-radius: 12px;
          overflow: hidden;
        }

        .email-header {
          border-radius: 12px 12px 0 0;
        }

        .email-footer {
          border-radius: 0 0 12px 12px;
        }
      }
    </style>
  </head>
  <body style="margin: 0; padding: 20px; background-color: #f5f5f5">
    <div class="email-container">
      <div class="email-header">
        <a href="https://www.togeda.ai" target="_blank">
          <img
            src="https://www.togeda.ai/togeda_logo_white.png"
            width="180"
            alt="Your App Logo"
            style="border: 0; max-width: 180px; height: auto; display: block; margin: 0 auto"
          />
        </a>
      </div>
      <div class="email-body">
        <div class="email-content">
          <h2>🎉 New Booking Received!</h2>

          <p>Hi <strong>{{hostName}}</strong>,</p>

          <p>
            Exciting news! You've received a new booking for your experience <strong>{{experienceTitle}}</strong>. 
            Here are the details:
          </p>

          <!-- Earnings Alert -->
          <div class="earnings-alert">
            <p style="margin: 0; font-size: 16px;">💰 <strong>You've earned:</strong></p>
            <div class="amount">${{totalAmount}}</div>
            <p style="margin: 0; font-size: 14px; opacity: 0.8;">Payment will be processed and transferred to your account</p>
          </div>

          <div class="details-box">
            <h3>📋 Booking Details</h3>

            <div class="booking-detail">
              <span class="label">Booking ID:</span>
              <span class="value highlight">{{bookingId}}</span>
            </div>

            <div class="booking-detail">
              <span class="label">Experience:</span>
              <span class="value">{{experienceTitle}}</span>
            </div>

            <div class="booking-detail">
              <span class="label">Date:</span>
              <span class="value">{{bookingDate}}</span>
            </div>

            <div class="booking-detail">
              <span class="label">Time:</span>
              <span class="value">{{bookingTime}}</span>
            </div>

            <div class="booking-detail">
              <span class="label">Number of Guests:</span>
              <span class="value">{{guestCount}} {{guestCount == 1 ? 'person' : 'people'}}</span>
            </div>

            <div class="booking-detail">
              <span class="label">Location:</span>
              <span class="value">{{experienceLocation}}</span>
            </div>

            <div class="booking-detail">
              <span class="label">Total Earnings:</span>
              <span class="value highlight">${{totalAmount}}</span>
            </div>
          </div>

          <div class="guest-info">
            <h3 style="color: #00796b; margin: 0 0 10px 0">👤 Guest Information: {{guestName}}</h3>

            <div class="contact-item">
              <span class="icon">📧</span>
              <span>{{guestEmail}}</span>
            </div>

            {{#if guestPhone}}
            <div class="contact-item">
              <span class="icon">📱</span>
              <span>{{guestPhone}}</span>
            </div>
            {{/if}}
          </div>

          {{#if specialRequests}}
          <div style="background-color: #e3f2fd; padding: 15px; border-radius: 8px; margin: 20px 0">
            <h4 style="margin: 0 0 10px 0; color: #1976d2; font-size: 16px">📝 Special Requests from Guest</h4>
            <p style="margin: 0; font-style: italic; font-size: 14px">"{{specialRequests}}"</p>
          </div>
          {{/if}}

          <!-- Action Buttons -->
          <div class="action-buttons">
            <a href="{{hostDashboardUrl}}" class="cta-button">View in Dashboard</a>
            <a href="mailto:{{guestEmail}}?subject=Re: Your booking for {{experienceTitle}} ({{bookingId}})" class="cta-button secondary">Contact Guest</a>
          </div>

          <!-- Next Steps -->
          <div style="background-color: #fff3e0; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h4 style="margin: 0 0 15px 0; color: #f57c00; font-size: 16px;">📋 Next Steps</h4>
            <ul style="margin: 0; padding-left: 20px;">
              <li>Review the booking details and special requests</li>
              <li>Prepare for your experience on {{bookingDate}} at {{bookingTime}}</li>
              <li>Contact the guest if you need to clarify anything</li>
              <li>Ensure you have everything ready for {{guestCount}} {{guestCount == 1 ? 'guest' : 'guests'}}</li>
            </ul>
          </div>

          <p>
            We're excited to see another successful booking for your experience! Remember, providing an exceptional 
            experience leads to great reviews and more bookings.
          </p>

          <p>
            If you have any questions or need support, don't hesitate to reach out to our host support team.
          </p>

          <p>Best regards,<br /><strong>The Togeda.ai Host Team</strong></p>
        </div>
      </div>
      <div class="email-footer">
        <p>&copy; 2025 Togeda.ai. All rights reserved.</p>
        <p>Host support: <a href="mailto:<EMAIL>"><EMAIL></a></p>
      </div>
    </div>
  </body>
</html>
