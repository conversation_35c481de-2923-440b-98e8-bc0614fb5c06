"use client"

import { useState, useEffect } from "react"
import { <PERSON>, Clock, MapPin, CreditCard, ChevronUp, Info } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription } from "@/components/ui/alert"

import {
  useLocalExperiencesBooking,
  useBookingPayment,
  useLocalExperience,
} from "@/lib/domains/local-experiences"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import type { LocalExperience } from "@/lib/domains/local-experiences"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

interface ExperienceBookingFormProps {
  experience: LocalExperience
  isVisible: boolean
  onCancel: () => void
  preselectedDate?: string
  preselectedTime?: string
}

export function ExperienceBookingForm({
  experience,
  isVisible,
  onCancel,
  preselectedDate,
  preselectedTime,
}: ExperienceBookingFormProps) {
  const { createBooking, bookingError, isCreatingBooking } = useLocalExperiencesBooking()
  const { isProcessingPayment, paymentError, processPayment } = useBookingPayment()
  const { selectedExperienceAvailability, loadExperienceAvailability, isLoadingAvailability } =
    useLocalExperience()
  const user = useUser()

  const [guestCount, setGuestCount] = useState(experience.minGuests)
  const [selectedDate, setSelectedDate] = useState(preselectedDate || "")
  const [selectedTime, setSelectedTime] = useState(preselectedTime || "")
  const [specialRequests, setSpecialRequests] = useState("")
  const [contactEmail, setContactEmail] = useState("")
  const [contactPhone, setContactPhone] = useState("")
  const [emailError, setEmailError] = useState("")

  // Update form when preselected values change
  useEffect(() => {
    if (preselectedDate) {
      setSelectedDate(preselectedDate)
    }
    if (preselectedTime) {
      setSelectedTime(preselectedTime)
    }
  }, [preselectedDate, preselectedTime])

  // Load availability when date changes
  useEffect(() => {
    if (selectedDate && experience.id) {
      loadExperienceAvailability(experience.id, selectedDate)
    }
  }, [selectedDate, experience.id, loadExperienceAvailability])

  // Reset form when visibility changes (but preserve preselected values)
  useEffect(() => {
    if (isVisible) {
      setGuestCount(experience.minGuests)
      // Only reset date/time if no preselected values
      if (!preselectedDate) setSelectedDate("")
      if (!preselectedTime) setSelectedTime("")
      setSpecialRequests("")
      setContactEmail("")
      setContactPhone("")
      setEmailError("")
    }
  }, [isVisible, experience.minGuests, preselectedDate, preselectedTime])

  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
    }).format(price)
  }

  const formatDuration = (minutes: number) => {
    if (minutes < 60) return `${minutes} minutes`
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return remainingMinutes > 0 ? `${hours} hours ${remainingMinutes} minutes` : `${hours} hours`
  }

  const formatTimeSlot = (time: string) => {
    // Convert 24-hour format to 12-hour format
    const [hours, minutes] = time.split(":")
    const hour = parseInt(hours)
    const ampm = hour >= 12 ? "PM" : "AM"
    const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour
    return `${displayHour}:${minutes} ${ampm}`
  }

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!email) {
      return "Email is required"
    }
    if (!emailRegex.test(email)) {
      return "Please enter a valid email address"
    }
    return ""
  }

  const handleEmailChange = (email: string) => {
    setContactEmail(email)
    setEmailError(validateEmail(email))
  }

  const totalPrice = experience.pricing.basePrice * guestCount

  const handleBooking = async () => {
    if (!selectedDate || !selectedTime || !contactEmail || !user) {
      return
    }

    try {
      // Generate availabilityId from selected time
      const timeParts = selectedTime.split(":")
      const hour = timeParts[0].padStart(2, "0")
      const minute = timeParts[1].padStart(2, "0")
      const availabilityId = `slot-${hour}-${minute}`

      // Create booking first
      const bookingData: any = {
        experienceId: experience.id,
        experienceTitle: experience.title,
        experienceLocation: `${experience.location.city}, ${experience.location.country}`,
        experienceHost: experience.host.name,
        userId: user?.uid || "",
        userEmail: contactEmail,
        userName: user?.displayName || contactEmail.split("@")[0],
        date: selectedDate,
        time: selectedTime,
        availabilityId: availabilityId,
        guests: guestCount,
        pricing: {
          basePrice: experience.pricing.basePrice,
          guests: guestCount,
          subtotal: totalPrice,
          taxes: 0,
          fees: 0,
          total: totalPrice,
          currency: experience.pricing.currency,
        },
      }

      // Only add specialRequests if it has content (Firestore doesn't allow undefined)
      if (specialRequests.trim()) {
        bookingData.specialRequests = specialRequests.trim()
      }

      const bookingId = await createBooking(experience.id, bookingData)
      if (bookingId) {
        // Process payment
        await processPayment(experience.id, bookingId)
      }
    } catch (error) {
      console.error("Error creating booking:", error)
    }
  }

  const isFormValid =
    selectedDate &&
    selectedTime &&
    contactEmail &&
    !emailError &&
    validateEmail(contactEmail) === "" &&
    guestCount >= experience.minGuests &&
    guestCount <= experience.maxGuests &&
    user

  if (!isVisible) return null

  return (
    <div className="h-full max-h-[80vh] bg-background animate-in slide-in-from-right-2 duration-300 flex flex-col">
      {/* Header with collapse button */}
      <div className="flex items-center gap-2 p-4 lg:p-6 border-b bg-background shrink-0">
        <Button variant="ghost" size="sm" onClick={onCancel} className="h-8 w-8 p-0">
          <ChevronUp className="h-4 w-4" />
        </Button>
        <CreditCard className="h-5 w-5 text-primary" />
        <h3 className="font-semibold">Complete Your Booking</h3>
      </div>

      {/* Scrollable content area */}
      <div
        className="flex-1 overflow-y-auto overscroll-contain min-h-0"
        style={{ WebkitOverflowScrolling: "touch" }}
      >
        <div className="p-4 lg:p-6 space-y-6">
          {/* Experience Summary */}
          <div className="space-y-3">
            <h4 className="font-medium">{experience.title}</h4>
            <div className="flex flex-wrap gap-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-1">
                <MapPin className="h-3 w-3" />
                <span>{experience.location.city}</span>
              </div>
              <div className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                <span>{formatDuration(experience.duration)}</span>
              </div>
              <div className="flex items-center gap-1">
                <Users className="h-3 w-3" />
                <span>
                  {experience.minGuests}-{experience.maxGuests} guests
                </span>
              </div>
            </div>
          </div>

          <Separator />

          {/* Shared Experience Warning for per_max_guest */}
          {(experience.bookingModel === "per_max_guest" || !experience.bookingModel) && (
            <Alert className="border-blue-200 bg-blue-50">
              <Info className="h-4 w-4 text-blue-600" />
              <AlertDescription className="text-blue-800 text-sm">
                <strong>Shared Experience:</strong> You may be joined by other guests who have also
                booked this time slot. This creates a great opportunity to meet fellow travelers!
              </AlertDescription>
            </Alert>
          )}

          {/* Booking Form */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Left Column */}
            <div className="space-y-4">
              {/* Guest Count */}
              <div>
                <Label htmlFor="guests">Number of Guests</Label>
                <Input
                  id="guests"
                  type="text"
                  min={experience.minGuests}
                  max={experience.maxGuests}
                  value={guestCount}
                  onChange={(e) => {
                    const value = parseInt(e.target.value)
                    setGuestCount(isNaN(value) ? 0 : value)
                  }}
                  className="mt-1"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Min: {experience.minGuests}, Max: {experience.maxGuests}
                </p>
                {guestCount < experience.minGuests && (
                  <p className="text-xs text-amber-600 mt-1">
                    Minimum {experience.minGuests} guests required
                  </p>
                )}
                {guestCount > experience.maxGuests && (
                  <p className="text-xs text-amber-600 mt-1">
                    Maximum {experience.maxGuests} guests allowed
                  </p>
                )}
              </div>

              {/* Date Selection */}
              <div>
                <Label htmlFor="date">Preferred Date</Label>
                <Input
                  id="date"
                  type="date"
                  value={selectedDate}
                  onChange={(e) => setSelectedDate(e.target.value)}
                  min={new Date().toISOString().split("T")[0]}
                  className="mt-1"
                />
              </div>

              {/* Time Selection */}
              <div>
                <Label htmlFor="time">Preferred Time</Label>
                {isLoadingAvailability ? (
                  <div className="mt-1 p-2 border rounded-md text-sm text-muted-foreground">
                    Loading available times...
                  </div>
                ) : selectedExperienceAvailability?.timeSlots ? (
                  <Select value={selectedTime} onValueChange={setSelectedTime}>
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select a time slot" />
                    </SelectTrigger>
                    <SelectContent>
                      {selectedExperienceAvailability.timeSlots
                        .filter((slot) => slot.available)
                        .map((slot) => (
                          <SelectItem key={slot.availabilityId} value={slot.time}>
                            {formatTimeSlot(slot.time)}
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                ) : (
                  <div className="mt-1 p-2 border rounded-md text-sm text-muted-foreground">
                    No available time slots for this date
                  </div>
                )}
              </div>
            </div>

            {/* Right Column */}
            <div className="space-y-4">
              {/* Contact Information */}
              <div>
                <Label htmlFor="email">Contact Email *</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={contactEmail}
                  onChange={(e) => handleEmailChange(e.target.value)}
                  onBlur={(e) => setEmailError(validateEmail(e.target.value))}
                  className={`mt-1 ${
                    emailError ? "border-destructive focus:border-destructive" : ""
                  }`}
                  required
                />
                {emailError && <p className="text-xs text-destructive mt-1">{emailError}</p>}
              </div>

              <div>
                <Label htmlFor="phone">Contact Phone (Optional)</Label>
                <Input
                  id="phone"
                  type="tel"
                  placeholder="+****************"
                  value={contactPhone}
                  onChange={(e) => setContactPhone(e.target.value)}
                  className="mt-1"
                />
              </div>

              {/* Special Requests */}
              <div>
                <Label htmlFor="requests">Special Requests (Optional)</Label>
                <Textarea
                  id="requests"
                  placeholder="Any special requirements..."
                  value={specialRequests}
                  onChange={(e) => setSpecialRequests(e.target.value)}
                  className="mt-1"
                  rows={3}
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Price Summary */}
          <div className="bg-background/50 rounded-lg p-4 space-y-3">
            <h4 className="font-semibold">Price Summary</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>
                  {formatPrice(experience.pricing.basePrice, experience.pricing.currency)} ×{" "}
                  {guestCount} guest{guestCount > 1 ? "s" : ""}
                </span>
                <span>{formatPrice(totalPrice, experience.pricing.currency)}</span>
              </div>
              <Separator />
              <div className="flex justify-between font-semibold text-base">
                <span>Total</span>
                <span>{formatPrice(totalPrice, experience.pricing.currency)}</span>
              </div>
            </div>
          </div>

          {/* Error Message */}
          {(paymentError || bookingError) && (
            <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
              <p className="text-destructive text-sm">{paymentError || bookingError}</p>
            </div>
          )}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex gap-3 p-4 lg:p-6 border-t bg-background shrink-0">
        <Button variant="outline" onClick={onCancel} className="flex-1">
          Cancel
        </Button>
        <Button
          onClick={handleBooking}
          disabled={!isFormValid || isProcessingPayment || isCreatingBooking}
          className="flex-1"
        >
          {isProcessingPayment || isCreatingBooking
            ? "Processing..."
            : `Confirm and Pay ${formatPrice(totalPrice, experience.pricing.currency)}`}
        </Button>
      </div>

      {/* Terms */}
      <div className="text-center text-xs text-muted-foreground pb-4 shrink-0">
        <p>By booking, you agree to our terms of service and cancellation policy.</p>
      </div>
    </div>
  )
}
