"use client"

import { useState, useEffect } from "react"
import { MapP<PERSON>, Clock, Users, Star, Calendar, Check, Minus, Info } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { <PERSON>roll<PERSON><PERSON> } from "@/components/ui/scroll-area"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useLocalExperience, useExperienceModal } from "@/lib/domains/local-experiences"
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog"
import { ExperienceBookingForm } from "./experience-booking-form"

export function ExperienceDetailModal() {
  const { selectedExperience, isExperienceModalOpen, closeExperienceModal } = useExperienceModal()
  const {
    selectedExperienceAvailability,
    loadExperienceAvailability,
    isLoadingAvailability,
    availabilityError,
  } = useLocalExperience()

  const [selectedImageIndex, setSelectedImageIndex] = useState(0)
  const [showBookingForm, setShowBookingForm] = useState(false)
  const [selectedDate, setSelectedDate] = useState("")
  const [selectedTimeSlot, setSelectedTimeSlot] = useState("")
  const [mobileBookingStep, setMobileBookingStep] = useState<"initial" | "selection" | "form">(
    "initial"
  )

  // Load availability when experience is selected or date changes
  useEffect(() => {
    if (selectedExperience?.id) {
      // Use selected date or default to today
      const dateToLoad = selectedDate || new Date().toISOString().split("T")[0]
      loadExperienceAvailability(selectedExperience.id, dateToLoad)
    }
  }, [selectedExperience?.id, selectedDate, loadExperienceAvailability])

  // Set default date to today when experience is selected
  useEffect(() => {
    if (selectedExperience?.id && !selectedDate) {
      const today = new Date().toISOString().split("T")[0]
      setSelectedDate(today)
    }
  }, [selectedExperience?.id, selectedDate])

  // Reset booking form when modal closes
  useEffect(() => {
    if (!isExperienceModalOpen) {
      setShowBookingForm(false)
      setSelectedDate("")
      setSelectedTimeSlot("")
      setMobileBookingStep("initial")
    }
  }, [isExperienceModalOpen])

  if (!selectedExperience) return null

  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
    }).format(price)
  }

  const formatDuration = (minutes: number) => {
    if (minutes < 60) return `${minutes} minutes`
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return remainingMinutes > 0 ? `${hours} hours ${remainingMinutes} minutes` : `${hours} hours`
  }

  const handleBookNow = () => {
    setShowBookingForm(true)
    setMobileBookingStep("form")
  }

  const handleMobileBookNowInitial = () => {
    setMobileBookingStep("selection")
  }

  const handleCancelBooking = () => {
    setShowBookingForm(false)
    setMobileBookingStep("initial")
    setSelectedDate("")
    setSelectedTimeSlot("")
  }

  const handleDateChange = (date: string) => {
    setSelectedDate(date)
    setSelectedTimeSlot("") // Reset time slot when date changes
  }

  const handleTimeSlotChange = (timeSlot: string) => {
    setSelectedTimeSlot(timeSlot)
  }

  const formatTimeSlot = (time: string) => {
    // Convert 24-hour format to 12-hour format
    const [hours, minutes] = time.split(":")
    const hour = parseInt(hours)
    const ampm = hour >= 12 ? "PM" : "AM"
    const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour
    return `${displayHour}:${minutes} ${ampm}`
  }

  return (
    <Dialog open={isExperienceModalOpen} onOpenChange={closeExperienceModal}>
      <DialogContent
        className="max-w-6xl max-h-[90vh] p-0"
        aria-describedby="experience-description"
      >
        {/* Hidden DialogTitle for accessibility */}
        <DialogTitle className="sr-only">{selectedExperience.title}</DialogTitle>
        <div id="experience-description" className="sr-only">
          Experience details for {selectedExperience.title} hosted by {selectedExperience.host.name}
        </div>

        <div className="flex flex-col lg:flex-row h-full max-h-[calc(90vh-2rem)]">
          {/* Main Content */}
          <div className="flex-1 flex flex-col min-h-0">
            {/* Header */}
            <div className="flex items-center justify-between p-4 lg:p-6 border-b shrink-0">
              <div className="flex-1">
                <h2 className="text-xl lg:text-2xl font-bold">{selectedExperience.title}</h2>
                <p className="text-muted-foreground mt-1 text-sm lg:text-base">
                  Hosted by {selectedExperience.host.name}
                </p>
              </div>
            </div>

            {/* Scrollable Content */}
            <ScrollArea className="flex-1 min-h-0">
              <div className="p-4 lg:p-6 space-y-4 lg:space-y-6">
                {/* Image Gallery */}
                <div className="space-y-3 lg:space-y-4">
                  {/* Main Image */}
                  <div className="relative h-48 lg:h-64 bg-muted rounded-lg overflow-hidden">
                    {selectedExperience.images?.[selectedImageIndex] ? (
                      <img
                        src={selectedExperience.images[selectedImageIndex]}
                        alt={selectedExperience.title}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <MapPin className="h-16 w-16 text-muted-foreground" />
                      </div>
                    )}
                  </div>

                  {/* Thumbnail Gallery */}
                  {selectedExperience.images && selectedExperience.images.length > 1 && (
                    <div className="flex gap-2 overflow-x-auto">
                      {selectedExperience.images.map((image, index) => (
                        <button
                          key={index}
                          onClick={() => setSelectedImageIndex(index)}
                          className={`flex-shrink-0 w-16 h-16 rounded-md overflow-hidden border-2 ${
                            index === selectedImageIndex ? "border-primary" : "border-transparent"
                          }`}
                        >
                          <img
                            src={image}
                            alt={`${selectedExperience.title} ${index + 1}`}
                            className="w-full h-full object-cover"
                          />
                        </button>
                      ))}
                    </div>
                  )}
                </div>

                {/* Quick Info */}
                <div className="flex flex-wrap gap-3 lg:gap-4 text-xs lg:text-sm">
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span>
                      {selectedExperience.location.city}, {selectedExperience.location.country}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span>{formatDuration(selectedExperience.duration)}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-muted-foreground" />
                    <span>
                      {selectedExperience.minGuests}-{selectedExperience.maxGuests} guests
                    </span>
                  </div>
                  {selectedExperience.rating > 0 && (
                    <div className="flex items-center gap-2">
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      <span>
                        {selectedExperience.rating.toFixed(1)} ({selectedExperience.reviewCount}{" "}
                        reviews)
                      </span>
                    </div>
                  )}
                </div>

                {/* Categories */}
                {selectedExperience.categories.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {selectedExperience.categories.map((category) => (
                      <Badge key={category} variant="secondary">
                        {category.charAt(0).toUpperCase() + category.slice(1)}
                      </Badge>
                    ))}
                  </div>
                )}

                <Separator />

                {/* Description */}
                <div>
                  <h3 className="text-base lg:text-lg font-semibold mb-2 lg:mb-3">
                    About this experience
                  </h3>
                  <p className="text-muted-foreground leading-relaxed text-sm lg:text-base">
                    {selectedExperience.description}
                  </p>
                </div>

                <Separator />

                {/* What's Included */}
                <div>
                  <h3 className="text-base lg:text-lg font-semibold mb-2 lg:mb-3">
                    What's included
                  </h3>
                  <div className="space-y-2">
                    {selectedExperience.inclusions.map((inclusion, index) => (
                      <div key={index} className="flex items-center gap-3">
                        {inclusion.included ? (
                          <Check className="h-4 w-4 text-green-600" />
                        ) : (
                          <Minus className="h-4 w-4 text-muted-foreground" />
                        )}
                        <span
                          className={`text-sm lg:text-base ${inclusion.included ? "" : "text-muted-foreground"}`}
                        >
                          {inclusion.item}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>

                <Separator />

                {/* Host Information */}
                <div>
                  <h3 className="text-base lg:text-lg font-semibold mb-2 lg:mb-3">
                    Meet your host
                  </h3>
                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                      <span className="text-primary font-semibold">
                        {selectedExperience.host.name.charAt(0)}
                      </span>
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-sm lg:text-base">
                        {selectedExperience.host.name}
                      </h4>
                      {selectedExperience.host.bio && (
                        <p className="text-xs lg:text-sm text-muted-foreground mt-1">
                          {selectedExperience.host.bio}
                        </p>
                      )}

                      {/* Host Contact Information */}
                      {(selectedExperience.host.email || selectedExperience.host.phone) && (
                        <div className="mt-3 space-y-1">
                          <p className="text-xs font-medium text-muted-foreground">
                            Contact Information:
                          </p>
                          {selectedExperience.host.email && (
                            <div className="flex items-center gap-2 text-xs">
                              <span className="text-muted-foreground">Email:</span>
                              <a
                                href={`mailto:${selectedExperience.host.email}`}
                                className="text-primary hover:underline"
                              >
                                {selectedExperience.host.email}
                              </a>
                            </div>
                          )}
                          {selectedExperience.host.phone && (
                            <div className="flex items-center gap-2 text-xs">
                              <span className="text-muted-foreground">Phone:</span>
                              <a
                                href={`tel:${selectedExperience.host.phone}`}
                                className="text-primary hover:underline"
                              >
                                {selectedExperience.host.phone}
                              </a>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <Separator />

                {/* Cancellation Policy */}
                <div>
                  <h3 className="text-base lg:text-lg font-semibold mb-2 lg:mb-3">
                    Cancellation policy
                  </h3>
                  <p className="text-muted-foreground text-sm lg:text-base">
                    {selectedExperience.cancellationPolicy}
                  </p>
                </div>
              </div>
            </ScrollArea>
          </div>

          {/* Booking Panel - Right side on desktop, bottom on mobile */}
          <div className="lg:w-2/5 lg:border-l bg-muted/30 border-t lg:border-t-0 shrink-0 h-auto lg:h-full flex flex-col">
            {!showBookingForm ? (
              <div className="p-4 lg:p-6 space-y-4 lg:space-y-6 lg:max-h-[calc(90vh-2rem)] lg:overflow-y-auto">
                {/* Price - Always visible */}
                <div className="text-center">
                  <div className="text-2xl lg:text-3xl font-bold">
                    {formatPrice(
                      selectedExperience.pricing.basePrice,
                      selectedExperience.pricing.currency
                    )}
                  </div>
                  <div className="text-muted-foreground text-sm lg:text-base">per person</div>
                </div>

                {/* Mobile: Show only Book Now button initially */}
                <div className="lg:hidden">
                  {mobileBookingStep === "initial" && (
                    <Button onClick={handleMobileBookNowInitial} className="w-full" size="lg">
                      Book Now
                    </Button>
                  )}
                </div>

                {/* Desktop: Always show date/time selection, Mobile: Show only after clicking Book Now */}
                <div
                  className={`space-y-4 lg:space-y-6 ${mobileBookingStep === "initial" ? "hidden lg:block" : ""}`}
                >
                  {/* Mobile: Back button when in selection mode */}
                  {mobileBookingStep === "selection" && (
                    <div className="lg:hidden">
                      <Button
                        variant="ghost"
                        onClick={() => setMobileBookingStep("initial")}
                        className="w-full justify-start p-0 h-auto text-sm text-muted-foreground"
                      >
                        ← Back to details
                      </Button>
                    </div>
                  )}

                  {/* Date Selection */}
                  <div className="space-y-2">
                    <Label htmlFor="date-select" className="text-sm font-medium">
                      Select Date
                    </Label>
                    <Input
                      id="date-select"
                      type="date"
                      value={selectedDate}
                      onChange={(e) => handleDateChange(e.target.value)}
                      min={new Date().toISOString().split("T")[0]}
                      className="w-full"
                    />
                  </div>

                  {/* Shared Experience Warning for per_max_guest */}
                  {(selectedExperience.bookingModel === "per_max_guest" ||
                    !selectedExperience.bookingModel) && (
                    <Alert className="border-blue-200 bg-blue-50">
                      <Info className="h-4 w-4 text-blue-600" />
                      <AlertDescription className="text-blue-800 text-sm">
                        <strong>Shared Experience:</strong> You may be joined by other guests who
                        have also booked this time slot. This creates a great opportunity to meet
                        fellow travelers!
                      </AlertDescription>
                    </Alert>
                  )}

                  {/* Time Slot Selection */}
                  <div className="space-y-2">
                    <Label htmlFor="time-select" className="text-sm font-medium">
                      Select Time
                    </Label>
                    {isLoadingAvailability ? (
                      <div className="flex items-center justify-center gap-2 p-3 border rounded-md">
                        <Calendar className="h-4 w-4 text-muted-foreground animate-pulse" />
                        <span className="text-muted-foreground text-sm">Loading time slots...</span>
                      </div>
                    ) : availabilityError ? (
                      <div className="flex items-center justify-center gap-2 p-3 border border-red-200 rounded-md bg-red-50">
                        <Calendar className="h-4 w-4 text-red-500" />
                        <span className="text-red-500 text-sm">Error loading availability</span>
                      </div>
                    ) : selectedExperienceAvailability?.timeSlots ? (
                      <div className="grid grid-cols-2 gap-2 max-h-32 overflow-y-auto">
                        {selectedExperienceAvailability.timeSlots.map((slot) => (
                          <Button
                            key={slot.availabilityId}
                            variant={selectedTimeSlot === slot.time ? "default" : "outline"}
                            size="sm"
                            onClick={() => slot.available && handleTimeSlotChange(slot.time)}
                            disabled={!slot.available}
                            className={`text-xs ${
                              !slot.available ? "opacity-50 cursor-not-allowed" : ""
                            }`}
                            title={
                              !slot.available
                                ? selectedExperience.bookingModel === "per_session"
                                  ? "This session is fully booked"
                                  : `Fully booked (${slot.currentBookings}/${slot.maxGuests} guests)`
                                : selectedExperience.bookingModel === "per_session"
                                  ? "Private session available"
                                  : `Available (${slot.remainingSpots || slot.maxGuests} spots remaining)`
                            }
                          >
                            <div className="flex flex-col items-center">
                              <span>{formatTimeSlot(slot.time)}</span>
                              {(selectedExperience.bookingModel === "per_max_guest" ||
                                !selectedExperience.bookingModel) && (
                                <span className="text-xs opacity-70">
                                  {!slot.available
                                    ? "(Full)"
                                    : `(${slot.remainingSpots || slot.maxGuests} left)`}
                                </span>
                              )}
                              {selectedExperience.bookingModel === "per_session" &&
                                !slot.available && (
                                  <span className="text-xs opacity-70">(Booked)</span>
                                )}
                            </div>
                          </Button>
                        ))}
                      </div>
                    ) : (
                      <div className="flex items-center justify-center gap-2 p-3 border rounded-md">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span className="text-muted-foreground text-sm">
                          No time slots available
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Book Now Button */}
                  <Button
                    onClick={handleBookNow}
                    className="w-full"
                    size="lg"
                    disabled={
                      !selectedDate ||
                      !selectedTimeSlot ||
                      isLoadingAvailability ||
                      !!availabilityError ||
                      !selectedExperienceAvailability ||
                      selectedExperienceAvailability.timeSlots.filter((slot) => slot.available)
                        .length === 0
                    }
                  >
                    {!selectedDate
                      ? "Select Date"
                      : !selectedTimeSlot
                        ? "Select Time"
                        : isLoadingAvailability
                          ? "Loading..."
                          : availabilityError
                            ? "Unavailable"
                            : selectedExperienceAvailability?.timeSlots.filter(
                                  (slot) => slot.available
                                ).length === 0
                              ? "Fully Booked"
                              : "Book Now"}
                  </Button>
                </div>

                {/* Additional Info */}
                <div className="text-center text-xs lg:text-sm text-muted-foreground">
                  <p>Free cancellation up to 24 hours before</p>
                </div>
              </div>
            ) : (
              /* Booking Form replaces the booking panel */
              selectedExperience && (
                <div className="flex-1 flex flex-col min-h-0">
                  <ExperienceBookingForm
                    experience={selectedExperience}
                    isVisible={showBookingForm}
                    onCancel={handleCancelBooking}
                    preselectedDate={selectedDate}
                    preselectedTime={selectedTimeSlot}
                  />
                </div>
              )
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
