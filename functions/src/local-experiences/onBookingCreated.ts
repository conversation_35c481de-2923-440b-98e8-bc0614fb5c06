import * as functions from "firebase-functions"
import * as admin from "firebase-admin"
import { EmailService } from "../utils/email.service"

/**
 * Interface for experience booking data
 */
interface ExperienceBooking {
  id: string
  experienceId: string
  userId: string
  date: string
  time: string
  availabilityId: string
  guests: number
  totalAmount: number
  status: "pending" | "confirmed" | "cancelled" | "completed"
  paymentStatus: "pending" | "paid" | "failed" | "refunded"
  guestDetails: {
    name: string
    email: string
    phone?: string
  }
  specialRequests?: string
  stripeSessionId?: string
  stripePaymentIntentId?: string
  stripeCustomerId?: string
  createdAt: admin.firestore.Timestamp
  confirmedAt?: admin.firestore.Timestamp
  cancelledAt?: admin.firestore.Timestamp
  completedAt?: admin.firestore.Timestamp
  cancellationReason?: string
}

/**
 * Interface for local experience data
 */
interface LocalExperience {
  id: string
  title: string
  description: string
  shortDescription: string
  host: {
    name: string
    avatar?: string
    responseTime: string
    languages: string[]
    bio: string
    email?: string
    phone?: string
    internalHostEmail?: string
  }
  location: {
    address: string
    city: string
    state: string
    country: string
    zipCode: string
    coordinates?: {
      latitude: number
      longitude: number
    }
  }
  pricing: {
    basePrice: number
    currency: string
    priceType: "per_person" | "per_group"
  }
  duration: number
  maxGuests: number
  minGuests: number
  categories: string[]
  images: string[]
  inclusions: Array<{
    item: string
    included: boolean
  }>
  cancellationPolicy: string
  rating: number
  reviewCount: number
  isActive: boolean
  stripeProductId?: string
  bookingModel?: "per_session" | "per_max_guest"
  createdAt: admin.firestore.Timestamp
}

/**
 * Firebase Function that triggers when a new booking is created
 * Sends email notifications to both guest and host
 */
export const onBookingCreated = functions.firestore
  .document("localExperiences/{experienceId}/bookings/{bookingId}")
  .onCreate(
    async (snap: functions.firestore.QueryDocumentSnapshot, context: functions.EventContext) => {
      try {
        const { experienceId, bookingId } = context.params
        const bookingData = snap.data() as ExperienceBooking

        console.log(`New booking created: ${bookingId} for experience ${experienceId}`)

        // Only send emails for confirmed bookings (after payment)
        if (bookingData.status !== "confirmed" || bookingData.paymentStatus !== "paid") {
          console.log(`Booking ${bookingId} is not confirmed/paid yet, skipping email notifications`)
          return {
            success: true,
            message: "Booking not confirmed/paid, emails will be sent when confirmed",
            bookingId,
            experienceId,
          }
        }

        // Get the experience details
        const db = admin.firestore()
        const experienceDoc = await db.collection("localExperiences").doc(experienceId).get()

        if (!experienceDoc.exists) {
          console.error(`Experience document not found for experienceId: ${experienceId}`)
          return {
            success: false,
            error: "Experience document not found",
            bookingId,
            experienceId,
          }
        }

        const experienceData = experienceDoc.data() as LocalExperience

        console.log(`Processing email notifications for booking ${bookingId}`)

        // Prepare booking details for emails
        const bookingDetails = {
          bookingId,
          guestName: bookingData.guestDetails.name,
          guestEmail: bookingData.guestDetails.email,
          guestPhone: bookingData.guestDetails.phone,
          experienceTitle: experienceData.title,
          hostName: experienceData.host.name,
          hostEmail: experienceData.host.email,
          hostPhone: experienceData.host.phone,
          bookingDate: bookingData.date,
          bookingTime: bookingData.time,
          guestCount: bookingData.guests,
          totalAmount: bookingData.totalAmount,
          specialRequests: bookingData.specialRequests,
          experienceLocation: experienceData.location.address,
          experienceDescription: experienceData.shortDescription,
          cancellationPolicy: experienceData.cancellationPolicy,
        }

        // Send confirmation email to guest
        const guestEmailResult = await EmailService.sendBookingConfirmationEmail(
          bookingData.guestDetails.email,
          bookingDetails
        )

        // Send notification email to host (use internal email if available, fallback to public email)
        const hostEmail = experienceData.host.internalHostEmail || experienceData.host.email
        let hostEmailResult: { success: boolean; messageId?: string; error?: string } = {
          success: false,
          error: "No host email configured"
        }

        if (hostEmail) {
          hostEmailResult = await EmailService.sendHostNotificationEmail(hostEmail, {
            ...bookingDetails,
            hostName: experienceData.host.name,
          })
        } else {
          console.warn(`No host email found for experience ${experienceId}`)
        }

        console.log("Email notification results:", {
          guestEmail: guestEmailResult,
          hostEmail: hostEmailResult,
        })

        return {
          success: true,
          bookingId,
          experienceId,
          guestName: bookingData.guestDetails.name,
          experienceTitle: experienceData.title,
          emailResults: {
            guestEmailSent: guestEmailResult.success,
            hostEmailSent: hostEmailResult.success,
            guestEmailError: guestEmailResult.error,
            hostEmailError: hostEmailResult.error,
          },
        }
      } catch (error) {
        console.error("Error in onBookingCreated function:", error)

        // Don't throw the error to prevent function retries
        // Log the error and return a failure response
        return {
          success: false,
          error: error instanceof Error ? error.message : "Unknown error",
          bookingId: context.params.bookingId,
          experienceId: context.params.experienceId,
        }
      }
    }
  )
