/**
 * Email template configuration for Firebase Functions
 *
 * This file contains the template IDs for emails sent via Brevo.
 * These should match the templates defined in the main app but are kept separate
 * to avoid import issues during Firebase Functions deployment.
 */

export const EmailTemplates = {
  // Template for squad invitations
  INVITATION: process.env.BREVO_INVITATION_TEMPLATE_ID
    ? Number(process.env.BREVO_INVITATION_TEMPLATE_ID)
    : 166,

  INVITATION_NEW_USER: process.env.BREVO_INVITATION_NEW_USER_TEMPLATE_ID
    ? Number(process.env.BREVO_INVITATION_NEW_USER_TEMPLATE_ID)
    : 169,

  // Template for squad member joined notification
  SQUAD_MEMBER_JOINED: process.env.BREVO_SQUAD_MEMBER_JOINED_TEMPLATE_ID
    ? Number(process.env.BREVO_SQUAD_MEMBER_JOINED_TEMPLATE_ID)
    : 170,

  TRIP_STARTED: process.env.BREVO_TRIP_STARTED_TEMPLATE_ID
    ? Number(process.env.BREVO_TRIP_STARTED_TEMPLATE_ID)
    : 167,

  TRIP_COMPLETED: process.env.BREVO_TRIP_COMPLETED_TEMPLATE_ID
    ? Number(process.env.BREVO_TRIP_COMPLETED_TEMPLATE_ID)
    : 168,

  // Template for welcome emails
  WELCOME: process.env.BREVO_WELCOME_TEMPLATE_ID
    ? Number(process.env.BREVO_WELCOME_TEMPLATE_ID)
    : undefined,

  // Template for password reset
  PASSWORD_RESET: process.env.BREVO_PASSWORD_RESET_TEMPLATE_ID
    ? Number(process.env.BREVO_PASSWORD_RESET_TEMPLATE_ID)
    : undefined,

  // Local Experience Templates
  EXPERIENCE_BOOKING_CONFIRMATION: process.env.BREVO_EXPERIENCE_BOOKING_CONFIRMATION_TEMPLATE_ID
    ? Number(process.env.BREVO_EXPERIENCE_BOOKING_CONFIRMATION_TEMPLATE_ID)
    : 171,

  EXPERIENCE_HOST_NOTIFICATION: process.env.BREVO_EXPERIENCE_HOST_NOTIFICATION_TEMPLATE_ID
    ? Number(process.env.BREVO_EXPERIENCE_HOST_NOTIFICATION_TEMPLATE_ID)
    : 172,

  EXPERIENCE_BOOKING_CANCELLED: process.env.BREVO_EXPERIENCE_BOOKING_CANCELLED_TEMPLATE_ID
    ? Number(process.env.BREVO_EXPERIENCE_BOOKING_CANCELLED_TEMPLATE_ID)
    : undefined,
}
