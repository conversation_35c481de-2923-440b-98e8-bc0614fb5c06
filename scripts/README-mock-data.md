# Local Experiences Setup

This directory contains scripts and data for setting up local experiences in development.

## Files

- `mock-experiences.json` - Mock experience data with all required fields
- `initialize-experiences.cjs` - **Main script** to set up everything
- `check-availability.cjs` - <PERSON><PERSON><PERSON> to verify setup (optional)
- `reset-availability-system.cjs` - <PERSON><PERSON><PERSON> to reset availability data (cleanup)

## Quick Setup

To set up local experiences from scratch:

```bash
node scripts/initialize-experiences.cjs
```

This single script will:

1. Load experiences from `mock-experiences.json`
2. Add them to Firestore with proper timestamps
3. Create default availability entries for each experience
4. Set up 4 time slots per experience (09:00, 11:00, 14:00, 16:00)

## Verification (Optional)

To verify the setup worked correctly:

```bash
node scripts/check-availability.cjs
```

## Reset/Cleanup

To reset the availability system:

```bash
node scripts/reset-availability-system.cjs
```

## Mock Experiences Included

1. **Sunset Kayaking Adventure** - $89 (per_session booking)

   - 3-hour guided kayaking tour in San Francisco Bay
   - Host: <PERSON>
   - Categories: adventure, outdoor

2. **Authentic Italian Cooking Class** - $125 (per_max_guest booking)

   - 4-hour hands-on cooking class in North Beach
   - Host: <PERSON>
   - Categories: food, culture, indoor

3. **Street Art & Murals Walking Tour** - $45 (per_max_guest booking)
   - 2.5-hour guided tour through Mission District
   - Host: Carlos Mendoza
   - Categories: culture, outdoor

Each experience includes:

- Complete host information with contact details
- Detailed location data
- Pricing breakdown
- Booking model configuration
- High-quality images
- Inclusions/exclusions
- Cancellation policies
- Sample ratings and reviews
