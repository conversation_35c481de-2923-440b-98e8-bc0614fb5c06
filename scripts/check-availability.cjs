#!/usr/bin/env node

/**
 * <PERSON>ript to check availability data for local experiences
 * Useful for debugging and verifying availability data
 */

const admin = require("firebase-admin")
const dotenv = require("dotenv")

// Load environment variables
dotenv.config({ path: ".env.local" })

// Initialize Firebase Admin SDK
if (!admin.apps.length) {
  const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY || "{}")

  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || "brotrip-mvp",
  })
}

const db = admin.firestore()

/**
 * Check availability for all experiences
 */
async function checkAvailability() {
  try {
    console.log("🔍 Checking availability data...")

    // Get all experiences
    const experiencesSnapshot = await db.collection("localExperiences").get()

    if (experiencesSnapshot.empty) {
      console.log("❌ No experiences found.")
      return
    }

    console.log(`📋 Found ${experiencesSnapshot.docs.length} experiences\n`)

    // Check today's availability for each experience
    const today = new Date().toISOString().split("T")[0]

    for (const experienceDoc of experiencesSnapshot.docs) {
      const experienceData = experienceDoc.data()
      console.log(`🎯 ${experienceData.title}`)
      console.log(`   ID: ${experienceDoc.id}`)

      // Get today's availability
      const availabilityRef = experienceDoc.ref.collection("availability").doc(today)
      const availabilitySnap = await availabilityRef.get()

      if (!availabilitySnap.exists) {
        console.log(`   ❌ No availability for today (${today})`)
      } else {
        const availabilityData = availabilitySnap.data()
        console.log(`   📅 Date: ${availabilityData.date}`)
        console.log(`   ⏰ Time slots: ${availabilityData.timeSlots.length}`)

        availabilityData.timeSlots.forEach((slot, index) => {
          const status = slot.available ? "✅ Available" : "❌ Unavailable"
          console.log(`      ${index + 1}. ${slot.time} (${slot.availabilityId}) - ${status}`)
          console.log(
            `         Max guests: ${slot.maxGuests}, Current bookings: ${slot.currentBookings}`
          )
        })
      }

      // Count total availability records
      const allAvailabilitySnapshot = await experienceDoc.ref.collection("availability").get()
      console.log(`   📊 Total availability records: ${allAvailabilitySnapshot.docs.length}`)
      console.log("")
    }

    console.log("✨ Availability check completed!")
  } catch (error) {
    console.error("❌ Error checking availability:", error)
    process.exit(1)
  }
}

/**
 * Main execution
 */
async function main() {
  try {
    await checkAvailability()
    process.exit(0)
  } catch (error) {
    console.error("❌ Script failed:", error)
    process.exit(1)
  }
}

// Run the script
if (require.main === module) {
  main()
}

module.exports = { checkAvailability }
