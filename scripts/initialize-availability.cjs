#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to initialize availability data for all local experiences
 * This creates availability slots for the next 30 days for all experiences
 */

const admin = require("firebase-admin")
const dotenv = require("dotenv")

// Load environment variables
dotenv.config({ path: ".env.vercel" })

// Initialize Firebase Admin SDK
if (!admin.apps.length) {
  const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY || "{}")

  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || "brotrip-mvp",
  })
}

const db = admin.firestore()

/**
 * Generate availability ID based on time slot
 * Format: "slot-HH-MM" (e.g., "slot-09-00", "slot-14-30")
 */
function generateAvailabilityId(time) {
  return `slot-${time.replace(":", "-")}`
}

/**
 * Generate time slots based on experience type and duration
 */
function generateTimeSlots(experienceData) {
  const timeSlots = []
  const duration = experienceData.duration || 120 // Default 2 hours
  const maxGuests = experienceData.maxGuests || 8

  // Define different schedules based on experience type
  if (experienceData.title.toLowerCase().includes("sunset")) {
    // Sunset experiences - evening slots only
    const times = ["17:00", "18:30"]
    times.forEach((time) => {
      timeSlots.push({
        availabilityId: generateAvailabilityId(time),
        time: time,
        available: true,
        maxGuests: maxGuests,
        currentBookings: 0,
      })
    })
  } else if (experienceData.title.toLowerCase().includes("cooking")) {
    // Cooking classes - morning and afternoon
    const times = ["10:00", "15:00"]
    times.forEach((time) => {
      timeSlots.push({
        availabilityId: generateAvailabilityId(time),
        time: time,
        available: true,
        maxGuests: maxGuests,
        currentBookings: 0,
      })
    })
  } else if (
    experienceData.title.toLowerCase().includes("night") ||
    experienceData.title.toLowerCase().includes("evening")
  ) {
    // Evening experiences
    const times = ["18:00", "19:30", "21:00"]
    times.forEach((time) => {
      timeSlots.push({
        availabilityId: generateAvailabilityId(time),
        time: time,
        available: true,
        maxGuests: maxGuests,
        currentBookings: 0,
      })
    })
  } else {
    // Regular day experiences - multiple slots throughout the day
    const times = ["09:00", "11:30", "14:00", "16:30"]
    times.forEach((time) => {
      timeSlots.push({
        availabilityId: generateAvailabilityId(time),
        time: time,
        available: true,
        maxGuests: maxGuests,
        currentBookings: 0,
      })
    })
  }

  return timeSlots
}

/**
 * Initialize default availability for all experiences
 * Uses the new scalable approach with default availability + date-specific overrides
 */
async function initializeAvailability() {
  try {
    console.log("🚀 Starting default availability initialization...")

    // Get all experiences
    const experiencesSnapshot = await db.collection("localExperiences").get()

    if (experiencesSnapshot.empty) {
      console.log("❌ No experiences found. Please run add-mock-experiences.js first.")
      return
    }

    console.log(`📋 Found ${experiencesSnapshot.docs.length} experiences`)

    // Process each experience
    let totalExperiencesProcessed = 0

    for (const experienceDoc of experiencesSnapshot.docs) {
      const experienceData = experienceDoc.data()
      console.log(`\n🎯 Processing: ${experienceData.title}`)

      // Step 1: Delete all existing availability documents (clean slate)
      console.log("   🗑️  Cleaning existing availability...")
      const existingAvailabilitySnapshot = await experienceDoc.ref.collection("availability").get()

      if (!existingAvailabilitySnapshot.empty) {
        const deleteBatch = db.batch()
        existingAvailabilitySnapshot.docs.forEach((doc) => {
          deleteBatch.delete(doc.ref)
        })
        await deleteBatch.commit()
        console.log(
          `   ✅ Deleted ${existingAvailabilitySnapshot.docs.length} existing availability documents`
        )
      }

      // Step 2: Generate time slots for this experience
      const timeSlots = generateTimeSlots(experienceData)
      console.log(`   ⏰ Generated ${timeSlots.length} default time slots`)

      // Step 3: Create default availability document
      const defaultAvailabilityRef = experienceDoc.ref.collection("availability").doc("default")

      const defaultAvailabilityData = {
        date: "default",
        timeSlots: timeSlots,
        isDefault: true,
      }

      await defaultAvailabilityRef.set(defaultAvailabilityData)
      console.log(`   ✅ Created default availability`)
      console.log(`   📋 Time slots: ${timeSlots.map((slot) => slot.time).join(", ")}`)

      totalExperiencesProcessed++
      console.log(`   🎉 Completed ${experienceData.title}`)
    }

    console.log(`\n🎊 SUCCESS! Initialized default availability for all experiences`)
    console.log(`📊 Total experiences processed: ${totalExperiencesProcessed}`)
    console.log(`\n📝 New System Benefits:`)
    console.log(`   ✅ Scalable: Only stores default + date-specific overrides`)
    console.log(`   ✅ Efficient: No need to create 30+ documents per experience`)
    console.log(`   ✅ Flexible: Can override specific dates when needed`)
    console.log(`   ✅ Validated: Bookings now check real availability against confirmed bookings`)
  } catch (error) {
    console.error("❌ Error initializing availability:", error)
    process.exit(1)
  }
}

/**
 * Main execution
 */
async function main() {
  try {
    await initializeAvailability()
    console.log("\n✨ Availability initialization completed successfully!")
    process.exit(0)
  } catch (error) {
    console.error("❌ Script failed:", error)
    process.exit(1)
  }
}

// Run the script
if (require.main === module) {
  main()
}

module.exports = { initializeAvailability, generateTimeSlots, generateAvailabilityId }
