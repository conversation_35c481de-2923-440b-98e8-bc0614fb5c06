#!/usr/bin/env node

/**
 * Reset Availability System Script
 * 
 * This script resets the availability system to use the new default-based approach:
 * 1. Removes all date-specific availability documents
 * 2. Creates a single "default" availability document for each experience
 * 3. Uses scalable approach where default availability is used unless date-specific overrides exist
 */

const admin = require("firebase-admin")

// Initialize Firebase Admin SDK
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.applicationDefault(),
  })
}

const db = admin.firestore()

/**
 * Generate availability ID based on time slot
 * Format: "slot-HH-MM" (e.g., "slot-09-00", "slot-14-30")
 */
function generateAvailabilityId(time) {
  return `slot-${time.replace(":", "-")}`
}

/**
 * Generate default time slots based on experience type and duration
 */
function generateDefaultTimeSlots(experienceData) {
  const timeSlots = []
  const duration = experienceData.duration || 120 // Default 2 hours
  const maxGuests = experienceData.maxGuests || 8

  // Generate time slots based on experience type
  if (experienceData.category === "food-drink") {
    // Food & drink experiences - meal times
    const mealTimes = ["11:30", "13:00", "18:00", "19:30"]
    mealTimes.forEach((time) => {
      timeSlots.push({
        availabilityId: generateAvailabilityId(time),
        time: time,
        available: true,
        maxGuests: maxGuests,
        currentBookings: 0,
      })
    })
  } else if (experienceData.category === "adventure") {
    // Adventure experiences - morning and afternoon slots
    const adventureTimes = ["09:00", "14:00"]
    adventureTimes.forEach((time) => {
      timeSlots.push({
        availabilityId: generateAvailabilityId(time),
        time: time,
        available: true,
        maxGuests: maxGuests,
        currentBookings: 0,
      })
    })
  } else {
    // Default: multiple slots throughout the day
    const defaultTimes = ["09:00", "11:30", "14:00", "16:30"]
    defaultTimes.forEach((time) => {
      timeSlots.push({
        availabilityId: generateAvailabilityId(time),
        time: time,
        available: true,
        maxGuests: maxGuests,
        currentBookings: 0,
      })
    })
  }

  return timeSlots
}

/**
 * Reset availability system for all experiences
 */
async function resetAvailabilitySystem() {
  try {
    console.log("🚀 Starting availability system reset...")

    // Get all experiences
    const experiencesSnapshot = await db.collection("localExperiences").get()
    
    if (experiencesSnapshot.empty) {
      console.log("❌ No experiences found.")
      return
    }

    console.log(`📋 Found ${experiencesSnapshot.docs.length} experiences`)

    let totalOperations = 0

    for (const experienceDoc of experiencesSnapshot.docs) {
      const experienceData = experienceDoc.data()
      console.log(`\n🎯 Processing: ${experienceData.title}`)

      // Step 1: Delete all existing availability documents
      console.log("   🗑️  Deleting existing availability documents...")
      const availabilitySnapshot = await experienceDoc.ref.collection("availability").get()
      
      if (!availabilitySnapshot.empty) {
        const batch = db.batch()
        let batchCount = 0

        availabilitySnapshot.docs.forEach((availabilityDoc) => {
          batch.delete(availabilityDoc.ref)
          batchCount++
          totalOperations++

          // Commit batch every 500 operations (Firestore limit)
          if (batchCount >= 500) {
            batch.commit()
            console.log(`     ✅ Deleted batch of ${batchCount} availability documents`)
            batchCount = 0
          }
        })

        // Commit remaining operations
        if (batchCount > 0) {
          await batch.commit()
          console.log(`     ✅ Deleted final batch of ${batchCount} availability documents`)
        }

        console.log(`     🗑️  Deleted ${availabilitySnapshot.docs.length} existing availability documents`)
      } else {
        console.log("     ℹ️  No existing availability documents found")
      }

      // Step 2: Create default availability
      console.log("   📅 Creating default availability...")
      const timeSlots = generateDefaultTimeSlots(experienceData)
      
      const defaultAvailability = {
        date: "default",
        timeSlots: timeSlots,
        isDefault: true,
      }

      const defaultAvailabilityRef = experienceDoc.ref.collection("availability").doc("default")
      await defaultAvailabilityRef.set(defaultAvailability)
      totalOperations++

      console.log(`     ✅ Created default availability with ${timeSlots.length} time slots`)
      console.log(`     📋 Time slots: ${timeSlots.map(slot => slot.time).join(", ")}`)
    }

    console.log(`\n🎉 Availability system reset completed!`)
    console.log(`📊 Total operations: ${totalOperations}`)
    console.log(`\n📝 New System Benefits:`)
    console.log(`   ✅ Scalable: Only stores default + date-specific overrides`)
    console.log(`   ✅ Efficient: No need to create 30+ documents per experience`)
    console.log(`   ✅ Flexible: Can override specific dates when needed`)
    console.log(`   ✅ Validated: Bookings now check real availability against confirmed bookings`)

  } catch (error) {
    console.error("❌ Error resetting availability system:", error)
    process.exit(1)
  }
}

// Run the script
resetAvailabilitySystem()
  .then(() => {
    console.log("\n✅ Script completed successfully!")
    process.exit(0)
  })
  .catch((error) => {
    console.error("❌ Script failed:", error)
    process.exit(1)
  })
