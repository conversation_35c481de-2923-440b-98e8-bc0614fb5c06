#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to programmatically set Firebase Functions environment variables
 *
 * This script reads environment variables from the main app's .env file
 * and sets them for Firebase Functions using the Firebase CLI.
 *
 * Usage:
 *   node scripts/set-functions-env.js
 *
 * Or with specific environment:
 *   node scripts/set-functions-env.js --env production
 */

import { execSync } from "child_process"
import fs from "fs"
import path from "path"

// Parse command line arguments
const args = process.argv.slice(2)
const envArg = args.find((arg) => arg.startsWith("--env="))
const environment = envArg ? envArg.split("=")[1] : "development"

console.log(`🔧 Setting Firebase Functions environment variables for: ${environment}`)

// Define the environment variables that should be set for Firebase Functions
const REQUIRED_ENV_VARS = [
  "BREVO_API_KEY",
  "EMAIL_FROM",
  "APP_BASE_URL",
  "BREVO_INVITATION_TEMPLATE_ID",
  "BREVO_INVITATION_NEW_USER_TEMPLATE_ID",
  "BREVO_SQUAD_MEMBER_JOINED_TEMPLATE_ID",
  "BREVO_TRIP_STARTED_TEMPLATE_ID",
  "BREVO_TRIP_COMPLETED_TEMPLATE_ID",
  "BREVO_WELCOME_TEMPLATE_ID",
  "BREVO_PASSWORD_RESET_TEMPLATE_ID",
  "BREVO_EXPERIENCE_BOOKING_CONFIRMATION_TEMPLATE_ID",
  "BREVO_EXPERIENCE_HOST_NOTIFICATION_TEMPLATE_ID",
  "BREVO_EXPERIENCE_BOOKING_CANCELLED_TEMPLATE_ID",
]

/**
 * Read environment variables from .env file
 */
function readEnvFile(envPath) {
  if (!fs.existsSync(envPath)) {
    console.warn(`⚠️  Environment file not found: ${envPath}`)
    return {}
  }

  const envContent = fs.readFileSync(envPath, "utf8")
  const envVars = {}

  envContent.split("\n").forEach((line) => {
    const trimmedLine = line.trim()
    if (trimmedLine && !trimmedLine.startsWith("#")) {
      const [key, ...valueParts] = trimmedLine.split("=")
      if (key && valueParts.length > 0) {
        // Remove quotes if present
        let value = valueParts.join("=").trim()
        if (
          (value.startsWith('"') && value.endsWith('"')) ||
          (value.startsWith("'") && value.endsWith("'"))
        ) {
          value = value.slice(1, -1)
        }
        envVars[key.trim()] = value
      }
    }
  })

  return envVars
}

/**
 * Convert environment variable name to Firebase config format
 */
function toFirebaseConfigName(envVarName) {
  return envVarName.toLowerCase().replace(/_/g, ".")
}

/**
 * Set Firebase Functions environment variables
 */
function setFirebaseFunctionsEnv(envVars) {
  const envPairs = []

  REQUIRED_ENV_VARS.forEach((varName) => {
    const value = process.env[varName] || envVars[varName]
    if (value) {
      const firebaseConfigName = toFirebaseConfigName(varName)
      envPairs.push(`${firebaseConfigName}="${value}"`)
      console.log(`✅ Found ${varName} -> ${firebaseConfigName}`)
    } else {
      console.warn(`⚠️  Missing ${varName}`)
    }
  })

  if (envPairs.length === 0) {
    console.error("❌ No environment variables found to set")
    process.exit(1)
  }

  // Set environment variables using Firebase CLI
  const command = `firebase functions:config:set ${envPairs.join(" ")}`

  console.log("\n🚀 Setting Firebase Functions environment variables...")
  console.log(`Command: ${command}`)

  try {
    execSync(command, { stdio: "inherit", cwd: process.cwd() })
    console.log("\n✅ Successfully set Firebase Functions environment variables!")

    // Show how to deploy
    console.log("\n📝 Next steps:")
    console.log("1. Deploy functions: firebase deploy --only functions")
    console.log("2. Or redeploy if already deployed: firebase functions:config:get")
  } catch (error) {
    console.error("\n❌ Failed to set environment variables:", error.message)
    process.exit(1)
  }
}

/**
 * Main execution
 */
function main() {
  try {
    // Determine which .env file to read
    const envFiles = [".env.local"]

    let envVars = {}

    // Read environment variables from files
    for (const envFile of envFiles) {
      const envPath = path.join(process.cwd(), envFile)
      if (fs.existsSync(envPath)) {
        console.log(`📖 Reading environment variables from: ${envFile}`)
        const fileEnvVars = readEnvFile(envPath)
        envVars = { ...envVars, ...fileEnvVars }
      }
    }

    // Also include process.env variables
    envVars = { ...envVars, ...process.env }

    setFirebaseFunctionsEnv(envVars)
  } catch (error) {
    console.error("❌ Error:", error.message)
    process.exit(1)
  }
}

// Run the script
main()
