#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to test Firebase Functions environment variables
 *
 * This script checks if all required environment variables are properly set
 * in Firebase Functions configuration.
 *
 * Usage:
 *   node scripts/test-functions-env.js
 */

import { execSync } from "child_process"

// Define the environment variables that should be set for Firebase Functions
const REQUIRED_ENV_VARS = [
  "BREVO_API_KEY",
  "EMAIL_FROM",
  "APP_BASE_URL",
  "BREVO_EXPERIENCE_BOOKING_CONFIRMATION_TEMPLATE_ID",
  "BREVO_EXPERIENCE_HOST_NOTIFICATION_TEMPLATE_ID",
]

const OPTIONAL_ENV_VARS = [
  "BREVO_INVITATION_TEMPLATE_ID",
  "BREVO_INVITATION_NEW_USER_TEMPLATE_ID",
  "BREVO_SQUAD_MEMBER_JOINED_TEMPLATE_ID",
  "BREVO_TRIP_STARTED_TEMPLATE_ID",
  "BREVO_TRIP_COMPLETED_TEMPLATE_ID",
  "BREVO_WELCOME_TEMPLATE_ID",
  "BREVO_PASSWORD_RESET_TEMPLATE_ID",
  "BREVO_EXPERIENCE_BOOKING_CANCELLED_TEMPLATE_ID",
]

/**
 * Get Firebase Functions configuration
 */
function getFunctionsConfig() {
  try {
    console.log("🔍 Fetching Firebase Functions configuration...")
    const output = execSync("firebase functions:config:get", { encoding: "utf8" })
    return JSON.parse(output)
  } catch (error) {
    console.error("❌ Failed to get Firebase Functions configuration:", error.message)
    return null
  }
}

/**
 * Check environment variables
 */
function checkEnvironmentVariables(config) {
  console.log("\n📋 Checking required environment variables...")

  let allRequired = true
  let optionalCount = 0

  // Check required variables
  REQUIRED_ENV_VARS.forEach((varName) => {
    const value = process.env[varName] || getConfigValue(config, varName)
    if (value) {
      console.log(`✅ ${varName}: Set`)
    } else {
      console.log(`❌ ${varName}: Missing (REQUIRED)`)
      allRequired = false
    }
  })

  console.log("\n📋 Checking optional environment variables...")

  // Check optional variables
  OPTIONAL_ENV_VARS.forEach((varName) => {
    const value = process.env[varName] || getConfigValue(config, varName)
    if (value) {
      console.log(`✅ ${varName}: Set`)
      optionalCount++
    } else {
      console.log(`⚠️  ${varName}: Not set (optional)`)
    }
  })

  console.log("\n📊 Summary:")
  console.log(`Required variables: ${allRequired ? "All set ✅" : "Missing some ❌"}`)
  console.log(`Optional variables: ${optionalCount}/${OPTIONAL_ENV_VARS.length} set`)

  return allRequired
}

/**
 * Get configuration value from Firebase Functions config
 */
function getConfigValue(config, varName) {
  if (!config) return null

  // Convert environment variable name to Firebase config path
  // e.g., BREVO_API_KEY -> brevo.api_key
  const parts = varName.toLowerCase().split("_")
  let current = config

  for (const part of parts) {
    if (current && typeof current === "object" && part in current) {
      current = current[part]
    } else {
      return null
    }
  }

  return current
}

/**
 * Main execution
 */
function main() {
  console.log("🧪 Testing Firebase Functions Environment Variables")
  console.log("=".repeat(50))

  try {
    const config = getFunctionsConfig()
    const allRequiredSet = checkEnvironmentVariables(config)

    if (allRequiredSet) {
      console.log("\n🎉 All required environment variables are properly configured!")
      console.log("\n📝 Next steps:")
      console.log("1. Deploy functions: npm run functions:deploy")
      console.log("2. Test booking creation to trigger email notifications")
    } else {
      console.log("\n⚠️  Some required environment variables are missing.")
      console.log("\n📝 To fix this:")
      console.log("1. Run: npm run functions:env")
      console.log("2. Or manually set missing variables with Firebase CLI")
      process.exit(1)
    }
  } catch (error) {
    console.error("❌ Error:", error.message)
    process.exit(1)
  }
}

// Run the script
main()
