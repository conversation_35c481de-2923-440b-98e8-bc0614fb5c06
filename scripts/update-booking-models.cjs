const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.applicationDefault(),
  });
}

const db = admin.firestore();

async function updateBookingModels() {
  console.log('🔄 Updating existing experiences with booking models...');

  try {
    // Get all experiences
    const experiencesSnapshot = await db.collection('localExperiences').get();
    
    if (experiencesSnapshot.empty) {
      console.log('❌ No experiences found');
      return;
    }

    console.log(`📋 Found ${experiencesSnapshot.docs.length} experiences to update`);

    const batch = db.batch();
    let updateCount = 0;

    experiencesSnapshot.docs.forEach((doc) => {
      const experience = doc.data();
      const experienceId = doc.id;
      
      // Skip if already has booking model
      if (experience.bookingModel) {
        console.log(`⏭️  Skipping ${experience.title} - already has booking model: ${experience.bookingModel}`);
        return;
      }

      let bookingModel;
      
      // Determine booking model based on experience type
      if (experience.title.toLowerCase().includes('kayaking') || 
          experience.title.toLowerCase().includes('sunset')) {
        bookingModel = 'per_session';
        console.log(`🛶 Setting ${experience.title} to per_session (private experience)`);
      } else if (experience.title.toLowerCase().includes('cooking') ||
                 experience.title.toLowerCase().includes('tour') ||
                 experience.title.toLowerCase().includes('class')) {
        bookingModel = 'per_max_guest';
        console.log(`👥 Setting ${experience.title} to per_max_guest (group experience)`);
      } else {
        // Default to per_max_guest for unknown types
        bookingModel = 'per_max_guest';
        console.log(`❓ Setting ${experience.title} to per_max_guest (default)`);
      }

      // Add to batch update
      batch.update(doc.ref, { bookingModel });
      updateCount++;
    });

    if (updateCount === 0) {
      console.log('✅ All experiences already have booking models');
      return;
    }

    // Commit the batch
    await batch.commit();
    console.log(`✅ Successfully updated ${updateCount} experiences with booking models`);

    // Verify updates
    console.log('\n📋 Final booking model assignments:');
    const updatedSnapshot = await db.collection('localExperiences').get();
    updatedSnapshot.docs.forEach((doc) => {
      const experience = doc.data();
      console.log(`  - ${experience.title}: ${experience.bookingModel}`);
    });

  } catch (error) {
    console.error('❌ Error updating booking models:', error);
    process.exit(1);
  }
}

// Run the update
updateBookingModels()
  .then(() => {
    console.log('\n🎉 Booking model update complete!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Fatal error:', error);
    process.exit(1);
  });
